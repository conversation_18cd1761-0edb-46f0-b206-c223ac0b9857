import React, {useEffect, useRef} from 'react';
import {
  NavigationContainer,
  NavigationContainerRef,
} from '@react-navigation/native';
import SplashScreen from 'react-native-splash-screen';
import {store} from './src/components/redux/store';
import {Linking, DeviceEventEmitter, Platform} from 'react-native';
import NavStack from './src/navigation/NavStack';
import Toast from 'react-native-toast-message';
import {Provider} from 'react-redux';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import backgroundSyncManager from './src/services/backgroundSyncManager';
import adMobService from './src/services/adMobService';

// Import first launch testing utilities for development
if (__DEV__) {
  require('./src/utils/testFirstLaunch');
}

const App = () => {
  const navigationRef = useRef<NavigationContainerRef<any>>(null);

  useEffect(() => {
    // Hide splash screen immediately
    SplashScreen.hide();

    // Initialize background sync manager and AdMob
    const initializeServices = async () => {
      try {
        console.log('[App] Starting services initialization...');

        // Initialize AdMob first
        await adMobService.initialize();
        console.log('[App] AdMob initialized successfully');

        // Then initialize background sync manager
        await backgroundSyncManager.initialize();
        console.log('[App] Background sync manager initialized successfully');
      } catch (error) {
        console.error('[App] Failed to initialize services:', error);
      }
    };

    initializeServices();

    // Handle deep links
    const handleDeepLink = (event: {url: string}) => {
      const url = event.url;
      console.log('[DeepLink] Deep link received:', url);

      if (url && navigationRef.current) {
        try {
          // Parse URL manually since URL constructor is not fully supported in React Native
          const urlParts = url.split('://');
          if (urlParts.length !== 2 || urlParts[0] !== 'indiacustomercare') {
            console.error('[DeepLink] Invalid URL scheme:', url);
            return;
          }

          const pathAndQuery = urlParts[1];
          const [path, queryString] = pathAndQuery.split('?');
          const screen = path;

          // Parse query parameters manually (URLSearchParams is not fully supported in React Native)
          const params: {[key: string]: string} = {};
          if (queryString) {
            queryString.split('&').forEach(param => {
              const [key, value] = param.split('=');
              if (key && value) {
                params[key] = decodeURIComponent(value);
              }
            });
          }

          const companyId = params['companyId'];
          const title = params['title'];

          console.log('[DeepLink] Parsed screen:', screen);
          console.log('[DeepLink] Company ID:', companyId);
          console.log('[DeepLink] Title:', title);

          if (screen === 'companydetails') {
            console.log(
              '[DeepLink] Navigating to CompanyDetailsScreen with companyId:',
              companyId,
              'and title:',
              title,
            );

            // Use navigationRef to navigate
            navigationRef.current?.navigate('CompanyDetailsScreen', {
              title: title || '',
              companyId: companyId ? parseInt(companyId, 10) : undefined,
              fromHistory: false,
            });
          }
        } catch (error) {
          console.error('[DeepLink] Error parsing deep link URL:', error);
        }
      } else {
        console.log('[DeepLink] No URL or navigation not ready:', {
          hasUrl: !!url,
          hasNavigation: !!navigationRef.current,
        });
      }
    };

    // Add event listener for deep links from React Native Linking API
    const linkingSubscription = Linking.addEventListener('url', handleDeepLink);

    // Add event listener for deep links from Android MainActivity (for shortcuts)
    let deviceEventSubscription = null;
    if (Platform.OS === 'android') {
      deviceEventSubscription = DeviceEventEmitter.addListener(
        'deepLink',
        (deepLink: string) => {
          console.log('[App] Deep link received from MainActivity:', deepLink);
          handleDeepLink({url: deepLink});
        },
      );
    }

    // Handle initial deep link if app is opened via shortcut
    console.log('[App] Checking for initial URL...');
    Linking.getInitialURL()
      .then(url => {
        console.log('[App] Initial URL:', url);
        if (url) {
          console.log('[App] Found initial URL, handling with delay...');
          // Add a small delay to ensure navigation is ready
          setTimeout(() => {
            handleDeepLink({url});
          }, 1000);
        } else {
          console.log('[App] No initial URL found');
        }
      })
      .catch(error => {
        console.error('[App] Error getting initial URL:', error);
      });

    // Cleanup on unmount
    return () => {
      backgroundSyncManager.cleanup();
      linkingSubscription.remove();
      if (deviceEventSubscription) {
        deviceEventSubscription.remove();
      }
    };
  }, []);

  return (
    <Provider store={store}>
      <SafeAreaProvider>
        <NavigationContainer ref={navigationRef}>
          <NavStack />
          <Toast />
        </NavigationContainer>
      </SafeAreaProvider>
    </Provider>
  );
};

export default App;
