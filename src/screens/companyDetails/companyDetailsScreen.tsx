import React, {useState, useLayoutEffect, useEffect, useRef} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Image,
  ScrollView,
  TouchableOpacity,
  Linking,
  ActivityIndicator,
} from 'react-native';
import {Text, Button} from 'react-native-paper';
import CustomTollFreeNumbersCard from '../../components/CustomTollFreeNumbersCard';
import {Images} from '../../assets';
import {useNavigation} from '@react-navigation/native';
import {COLORS, FONTS, CONFIG} from '../../common/constant';
import commonStyles from '../../common/commonStyles';
import NoteModalScreen from '../note/noteScreen';
import axios from 'axios';
import {showErrorToast} from '../../utils/showToast.js';
import CustomSearchBar from '../../components/CustomSearchBar';
import {useNetworkState} from '../../utils/networkStateManager';
import historyRepository from '../../database/watermelon/repositories/historyRepository';
import companyRepository from '../../database/watermelon/repositories/companyRepository';
import numberRepository from '../../database/watermelon/repositories/numberRepository';
import WebSocketVoteService from '../../socket/iccappush.js';
import ScreenshotService from '../../services/screenshotService';
import {useBankHolidayForCategory} from '../../hooks/useBankHoliday';
import BankHolidayBanner from '../../components/BankHolidayBanner';

// Contact number interface
export interface ContactNumber {
  numberId: number;
  companyId: number;
  number: string;
  description: string;
  type: string;
  upvoteCount: number;
  downvoteCount: number;
  isWhatsapp: boolean;
}

// Vote API response interfaces
export interface ContactVote {
  contactId: number;
  contactNumber: string;
  upVotes: number;
  downVotes: number;
}

export interface VoteApiResponse {
  message: {
    companyId: string;
    contactsVotes: ContactVote[];
  };
  status: number;
  success: boolean;
}

// Company details interface
export interface CompanyDetails {
  companyId: number;
  companyName: string;
  parentCompany: string | null;
  companyEmail: string | null;
  companyLogoUrl: string | null;
  companyCountry: string | null;
  companyAddress: string | null;
  companyWebsite: string | null;
  number: string | null; // Phone number field
  isWhatsapp: number | 0; // whatsapp number track
  upVoteCount: number;
  downVoteCount: number;
  createdAt: string;
  categories: Array<{
    categoryId: number;
    name: string;
    iconUrl: string;
    isActive: boolean;
  }>;
  contactNumbers: {
    TOLL_FREE: ContactNumber[];
    ALL_INDIA: ContactNumber[];
    INTERNATIONAL: ContactNumber[];
  };
}

const CompanyDetailsScreen = ({route}: {route: any}) => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const {isConnected} = useNetworkState();
  const [error, setError] = useState<string | null>(null);

  const [tollFreeCenter, setTollFreeCenter] = useState(false);
  const [indiaCallCenter, setIndiaCallCenter] = useState(false);
  const [internationalCenter, setInternationalCenter] = useState(false);
  const [hasLocalDataLoad, setHasLocalDataLoad] = useState(false);

  const [tollFreeNumbers, setTollFreeNumbers] = useState<ContactNumber[]>([]);
  const [allIndiaNumbers, setAllIndiaNumbers] = useState<ContactNumber[]>([]);
  const [internationalNumber, setInternationalNumber] = useState<
    ContactNumber[]
  >([]);

  const [localTollFreeNumbers, setLocalTollFreeNumbers] = useState<
    ContactNumber[]
  >([]);
  const [localAllIndiaNumbers, setLocalAllIndiaNumbers] = useState<
    ContactNumber[]
  >([]);
  const [localInternationalNumber, setLocalInternationalNumber] = useState<
    ContactNumber[]
  >([]);

  const [isModalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [companyInfo, setCompanyInfo] = useState<CompanyDetails | null>(null);
  const {title, companyId = '', fromHistory = false, categoryId} = route.params;
  const [isSearching, setIsSearching] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  // Ref for capturing screenshot of the main content
  const screenContentRef = useRef(null);
  // Add state to track vote counts fetch status
  const [voteCountsFetched, setVoteCountsFetched] = useState(false);

  // Use bank holiday hook with categoryId (only method now)
  const {isBankCategory, bankHolidayMessage} = useBankHolidayForCategory(
    categoryId || 0,
  );

  // Debug logging
  console.log(
    `[CompanyDetailsScreen] Bank holiday detection - CategoryId: ${categoryId}, Result: ${
      isBankCategory ? 'Holiday' : 'No holiday'
    }`,
  );

  // Try to load numbers from local database first
  const loadLocalNumbers = async (companyIdNum: number): Promise<boolean> => {
    try {
      const localNumbers = await numberRepository.getByCompanyIdGrouped(
        companyIdNum,
      );

      // Check if we have any numbers locally
      const hasTollFree = localNumbers.TOLL_FREE.length > 0;
      const hasAllIndia = localNumbers.ALL_INDIA.length > 0;
      const hasInternational = localNumbers.INTERNATIONAL.length > 0;

      if (hasTollFree || hasAllIndia || hasInternational) {
        console.log(
          `[CompanyDetails] Found ${
            localNumbers.TOLL_FREE.length +
            localNumbers.ALL_INDIA.length +
            localNumbers.INTERNATIONAL.length
          } numbers locally for company ${companyIdNum}`,
        );

        // Convert local numbers to ContactNumber format
        const convertToContactNumber = (localNum: any): ContactNumber => ({
          numberId: localNum.api_number_id,
          companyId: localNum.company_id,
          number: localNum.number,
          description: localNum.description || '',
          type: localNum.type,
          upvoteCount: localNum.upvote_count || 0,
          downvoteCount: localNum.downvote_count || 0,
          isWhatsapp: localNum.is_whatsapp === 1,
        });

        // Set the numbers
        if (hasTollFree) {
          setTollFreeNumbers(
            localNumbers.TOLL_FREE.map(convertToContactNumber),
          );
          setLocalTollFreeNumbers(
            localNumbers.TOLL_FREE.map(convertToContactNumber),
          );
        }
        if (hasAllIndia) {
          setAllIndiaNumbers(
            localNumbers.ALL_INDIA.map(convertToContactNumber),
          );
          setLocalAllIndiaNumbers(
            localNumbers.ALL_INDIA.map(convertToContactNumber),
          );
        }
        if (hasInternational) {
          setInternationalNumber(
            localNumbers.INTERNATIONAL.map(convertToContactNumber),
          );
          setLocalInternationalNumber(
            localNumbers.INTERNATIONAL.map(convertToContactNumber),
          );
        }

        // Set default open section - prioritize Toll Free if available
        if (hasTollFree) {
          setTollFreeCenter(true);
        } else if (hasAllIndia) {
          setIndiaCallCenter(true);
        } else if (hasInternational) {
          setInternationalCenter(true);
        }

        return true; // Successfully loaded from local
      }

      return false; // No local numbers found
    } catch (error) {
      console.error('[CompanyDetails] Error loading local numbers:', error);
      return false;
    }
  };

  const getComapnyDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      // First, try to load numbers from local database
      const companyIdNum = parseInt(companyId.toString(), 10);
      const hasLocalNumbers = await loadLocalNumbers(companyIdNum);
      setHasLocalDataLoad(hasLocalNumbers); // To encounter, Screen data load with local database or api
      // Only call API if we don't have local numbers
      const shouldCallApi = !hasLocalNumbers;

      if (shouldCallApi) {
        console.log(
          `[CompanyDetails] Loading company details from API for company ${companyId} (no local numbers found)`,
        );

        const response = await axios.get(
          `${CONFIG.API_URL}/company/${companyId}`,
          {
            headers: {
              Accept: 'application/json',
              'Content-Type': 'application/json',
            },
            timeout: 30000, // 30 seconds timeout
          },
        );

        console.info(`
          ====================================
          ============== Request =============
          URL: ${CONFIG.API_URL}/company/${companyId}
          Method: GET
          Headers: ${JSON.stringify(
            {
              Accept: 'application/json',
              'Content-Type': 'application/json',
            },
            null,
            2,
          )}
          ============= Response =============
          StatusCode: ${response.status}
          Response: ${JSON.stringify(response.data, null, 2)}
          ====================================
        `);

        if (response.status === 200 && response.data.data) {
          setCompanyInfo(response.data.data);

          // Save company details to local database for history
          try {
            // Convert API company format to local database format
            const companyForDb = {
              company_id: response.data.data.companyId, // Store original API companyId
              company_name: response.data.data.companyName,
              parent_company: response.data.data.parentCompany,
              company_email: response.data.data.companyEmail,
              company_logo_url: response.data.data.companyLogoUrl,
              company_country: response.data.data.companyCountry,
              company_address: response.data.data.companyAddress,
              company_website: response.data.data.companyWebsite,
              number: response.data.data.number, // Store phone number
              is_whatsapp: response.data.data.isWhatsapp,
              upvote_count: response.data.data.upVoteCount,
              downvote_count: response.data.data.downVoteCount,
            };

            // Use createOrUpdate to handle both create and update cases
            await companyRepository.createOrUpdateByCompanyId(companyForDb);
            console.log(
              'Saved/Updated company in local database:',
              response.data.data.companyId,
            );
          } catch (dbError) {
            console.error('Failed to save company to local database:', dbError);
          }

          // Store contact numbers from API (this will override local numbers if API has them)
          if (response.data.data.contactNumbers) {
            let hasTollFree = false;
            let hasAllIndia = false;
            let hasInternational = false;

            if (
              response.data.data.contactNumbers.TOLL_FREE &&
              response.data.data.contactNumbers.TOLL_FREE.length > 0
            ) {
              console.log(
                'response.data.data.contactNumbers.TOLL_FREE :',
                response.data.data.contactNumbers.TOLL_FREE,
              );
              setTollFreeNumbers(response.data.data.contactNumbers.TOLL_FREE);
              hasTollFree = true;
            }

            if (
              response.data.data.contactNumbers.ALL_INDIA &&
              response.data.data.contactNumbers.ALL_INDIA.length > 0
            ) {
              console.log(
                'response.data.data.contactNumbers.ALL_INDIA :',
                response.data.data.contactNumbers.ALL_INDIA,
              );
              setAllIndiaNumbers(response.data.data.contactNumbers.ALL_INDIA);
              hasAllIndia = true;
            }

            if (
              response.data.data.contactNumbers.INTERNATIONAL &&
              response.data.data.contactNumbers.INTERNATIONAL.length > 0
            ) {
              setInternationalNumber(
                response.data.data.contactNumbers.INTERNATIONAL,
              );
              hasInternational = true;
            }

            // Set default open section - prioritize Toll Free if available
            if (hasTollFree) {
              setTollFreeCenter(true);
            } else if (hasAllIndia) {
              setIndiaCallCenter(true);
            } else if (hasInternational) {
              setInternationalCenter(true);
            }
          }
        }
      } else {
        console.log(
          `[CompanyDetails] Using local numbers for company ${companyId}, skipping API call`,
        );

        // Try to get basic company info from local database
        try {
          const localCompany = await companyRepository.getByCompanyId(
            companyIdNum,
          );
          if (localCompany) {
            // Create a basic CompanyDetails object from local data
            const basicCompanyInfo: CompanyDetails = {
              companyId: localCompany.company_id || companyIdNum,
              companyName: localCompany.company_name,
              parentCompany: localCompany.parent_company || null,
              companyEmail: localCompany.company_email || null,
              companyLogoUrl: localCompany.company_logo_url || null,
              companyCountry: localCompany.company_country || null,
              companyAddress: localCompany.company_address || null,
              companyWebsite: localCompany.company_website || null,
              number: localCompany.number || null,
              isWhatsapp: localCompany.is_whatsapp || 0,
              upVoteCount: localCompany.upvote_count || 0,
              downVoteCount: localCompany.downvote_count || 0,
              createdAt: localCompany.created_at?.toISOString() || '',
              categories: [], // We'll need to fetch this separately if needed
              contactNumbers: {
                TOLL_FREE: [],
                ALL_INDIA: [],
                INTERNATIONAL: [],
              },
            };

            setCompanyInfo(basicCompanyInfo);
            console.log('companyInfo from local:', companyInfo);
          }
        } catch (localError) {
          console.error(
            '[CompanyDetails] Error loading local company info:',
            localError,
          );
        }
      }
    } catch (err: any) {
      console.error('[CompanyDetails] API Error:', err);

      let errorMessage = 'Failed to load company details';

      if (err.response) {
        // Server responded with error status
        const status = err.response.status;
        const data = err.response.data;

        console.error(`[CompanyDetails] Server Error: ${status}`, data);

        if (status === 404) {
          errorMessage = 'Company not found';
        } else if (status === 500) {
          errorMessage = 'Server error. Please try again later';
        } else if (status >= 400 && status < 500) {
          errorMessage = data?.message || 'Invalid request';
        } else {
          errorMessage = data?.message || `Server error (${status})`;
        }
      } else if (err.request) {
        // Network error
        console.error('[CompanyDetails] Network Error:', err.request);
        errorMessage = 'Network error. Please check your connection';
      } else if (err.code === 'ECONNABORTED') {
        // Timeout error
        console.error('[CompanyDetails] Timeout Error');
        errorMessage = 'Request timeout. Please try again';
      } else {
        // Other error
        console.error('[CompanyDetails] Unknown Error:', err.message);
        errorMessage = err.message || 'An unexpected error occurred';
      }

      showErrorToast(errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Fetch vote counts for company numbers
  const fetchVoteCounts = async (forceRefresh = false) => {
    if (!isConnected) {
      console.log(
        '[CompanyDetails] No network connection, skipping vote count fetch',
      );
      return;
    }

    if (!companyId) {
      console.log(
        '[CompanyDetails] No companyId provided, skipping vote count fetch',
      );
      return;
    }

    // Prevent multiple calls unless forced refresh
    if (voteCountsFetched && !forceRefresh) {
      console.log(
        '[CompanyDetails] Vote counts already fetched, skipping duplicate call',
      );
      return;
    }

    try {
      console.log(
        `[CompanyDetails] Fetching vote counts for company ${companyId}`,
      );

      const response = await axios.post(
        `${CONFIG.API_URL}/company/up-down`,
        {
          companyId: parseInt(companyId.toString(), 10),
        },
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 seconds timeout
        },
      );

      console.info(`
        ====================================
        ============== Request =============
        URL: ${CONFIG.API_URL}/company/up-down
        Method: POST
        Headers: ${JSON.stringify(
          {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
          null,
          2,
        )}
        Body: ${JSON.stringify(
          {companyId: parseInt(companyId.toString(), 10)},
          null,
          2,
        )}
        ============= Response =============
        StatusCode: ${response.status}
        Response: ${JSON.stringify(response.data, null, 2)}
        ====================================
      `);

      if (
        response.status === 200 &&
        response.data?.success &&
        response.data?.message?.contactsVotes
      ) {
        const voteData: VoteApiResponse = response.data;
        updateContactNumbersWithVotes(voteData.message.contactsVotes);
        setVoteCountsFetched(true);
        console.log(
          '[CompanyDetails] Vote counts successfully fetched and updated',
        );
      }
    } catch (err: any) {
      console.error('[CompanyDetails] Vote API Error:', err);

      // Don't show error toast for vote API failures as it's not critical
      // Just log the error for debugging
      if (err.response) {
        console.error(
          `[CompanyDetails] Vote API Server Error: ${err.response.status}`,
          err.response.data,
        );
      } else if (err.request) {
        console.error('[CompanyDetails] Vote API Network Error:', err.request);
      } else {
        console.error('[CompanyDetails] Vote API Unknown Error:', err.message);
      }
    }
  };

  // Update contact numbers with fresh vote counts
  const updateContactNumbersWithVotes = (contactsVotes: ContactVote[]) => {
    console.log(
      '[CompanyDetails] Updating contact numbers with vote counts:',
      contactsVotes,
    );

    // Helper function to update a number array with vote data
    const updateNumberArray = (numbers: ContactNumber[]): ContactNumber[] => {
      return numbers.map(number => {
        // Find matching vote data by contactId (matches numberId) or by phone number
        const voteData = contactsVotes.find(
          vote =>
            vote.contactId === number.numberId ||
            vote.contactNumber === number.number,
        );

        if (voteData) {
          console.log(
            `[CompanyDetails] Updating votes for number ${number.number}: ${voteData.upVotes} up, ${voteData.downVotes} down`,
          );

          // Update local database with fresh vote counts
          numberRepository
            .updateVoteCounts(
              number.numberId,
              voteData.upVotes,
              voteData.downVotes,
            )
            .catch(error => {
              console.error(
                '[CompanyDetails] Failed to update local DB vote counts:',
                error,
              );
            });

          return {
            ...number,
            upvoteCount: voteData.upVotes,
            downvoteCount: voteData.downVotes,
          };
        }

        return number;
      });
    };

    // Update all number arrays
    setTollFreeNumbers(prev => updateNumberArray(prev));
    setAllIndiaNumbers(prev => updateNumberArray(prev));
    setInternationalNumber(prev => updateNumberArray(prev));
  };

  // Callback function to handle vote updates from CustomTollFreeNumbersCard
  const handleVoteUpdate = async (numberId: number, isUpvote: boolean) => {
    try {
      console.log(
        `[CompanyDetails] Handling ${
          isUpvote ? 'upvote' : 'downvote'
        } for number ${numberId}`,
      );

      // Update local database
      const success = isUpvote
        ? await numberRepository.incrementUpvote(numberId)
        : await numberRepository.incrementDownvote(numberId);

      if (success) {
        console.log(
          `[CompanyDetails] Successfully updated local DB for number ${numberId}`,
        );

        // Update the UI state to reflect the change
        const updateArrayWithVote = (
          numbers: ContactNumber[],
        ): ContactNumber[] => {
          return numbers.map(number => {
            if (number.numberId === numberId) {
              return {
                ...number,
                upvoteCount: isUpvote
                  ? number.upvoteCount + 1
                  : number.upvoteCount,
                downvoteCount: !isUpvote
                  ? number.downvoteCount + 1
                  : number.downvoteCount,
              };
            }
            return number;
          });
        };

        // Update all number arrays
        setTollFreeNumbers(prev => updateArrayWithVote(prev));
        setAllIndiaNumbers(prev => updateArrayWithVote(prev));
        setInternationalNumber(prev => updateArrayWithVote(prev));
      } else {
        console.warn(
          `[CompanyDetails] Failed to update local DB for number ${numberId}`,
        );
      }
    } catch (error) {
      console.error('[CompanyDetails] Error handling vote update:', error);
    }
  };

  // Add company to history when viewed
  const addToHistory = async () => {
    if (companyId && !fromHistory) {
      try {
        // Convert companyId to string for WatermelonDB
        const companyIdString = companyId.toString();

        await historyRepository.addToHistory(companyIdString);
        console.log('Company added to history:', companyIdString);
      } catch (error) {
        console.error('Failed to add company to history:', error);
      }
    }
  };

  useEffect(() => {
    getComapnyDetails();

    // Initialize WebSocket connection early for voting functionality (but don't block if it fails)
    if (isConnected) {
      // Use a longer timeout to avoid blocking the UI and let the screen fully load
      setTimeout(() => {
        WebSocketVoteService.initializeConnection(0, 1)
          .then(success => {
            if (success) {
              console.log(
                '[CompanyDetails] WebSocket connection initialized successfully',
              );
            } else {
              console.log(
                '[CompanyDetails] WebSocket initialization failed, will retry when voting',
              );
            }
          })
          .catch(error => {
            console.log(
              '[CompanyDetails] WebSocket initialization error:',
              error,
            );
          });
      }, 3000); // Delay by 3 seconds to let the screen fully load first
    }

    // Cleanup function to disconnect WebSocket when component unmounts
    return () => {
      console.log(
        '[CompanyDetails] Component unmounting, disconnecting WebSocket',
      );
      WebSocketVoteService.disconnect();
    };
  }, []);

  // Reset vote counts flag when companyId changes (new company loaded)
  useEffect(() => {
    setVoteCountsFetched(false);
  }, [companyId]);

  // Fetch vote counts once when contact numbers are available and network is connected
  useEffect(() => {
    const hasNumbers =
      tollFreeNumbers.length > 0 ||
      allIndiaNumbers.length > 0 ||
      internationalNumber.length > 0;

    if (hasNumbers && isConnected && companyId && !voteCountsFetched) {
      console.log(
        '[CompanyDetails] Contact numbers loaded, fetching vote counts (once)',
      );
      fetchVoteCounts();
    }
  }, [
    tollFreeNumbers.length,
    allIndiaNumbers.length,
    internationalNumber.length,
    isConnected,
    companyId,
    voteCountsFetched,
  ]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title,
      headerRight: () => (
        <View style={styles.headerIconsContainer}>
          <TouchableOpacity onPress={() => handleNotePress()}>
            <Image source={Images.ic_note} style={styles.iconStyle} />
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => handleSharePress()}
            disabled={isSharing}
            style={[isSharing && {opacity: 0.6}]}>
            {isSharing ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Image source={Images.ic_shareFat} style={styles.iconStyle} />
            )}
          </TouchableOpacity>
        </View>
      ),
    });
  }, [navigation, title]);

  const handleNotePress = () => {
    // Handle Note icon press
    setModalVisible(!isModalVisible);
  };

  const handleSharePress = () => {
    // Handle Share icon press - capture and share screenshot
    captureAndShareScreenshot();
  };

  const captureAndShareScreenshot = async () => {
    const screenshotUri = await ScreenshotService.captureFullScreen();
    if (screenshotUri) {
      await ScreenshotService.shareScreenshot(screenshotUri);
    }
  };

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);

    if (!query || query.trim() === '') {
      // Reset to original data from companyInfo when search is cleared
      // Reset the data
      let hasTollFree = false;
      let hasAllIndia = false;
      let hasInternational = false;

      if (hasLocalDataLoad) {
        setTollFreeNumbers(localTollFreeNumbers);
        setAllIndiaNumbers(localAllIndiaNumbers);
        setInternationalNumber(localInternationalNumber);

        hasTollFree = localTollFreeNumbers && localTollFreeNumbers.length > 0;
        hasAllIndia = localAllIndiaNumbers && localAllIndiaNumbers.length > 0;
        hasInternational =
          localInternationalNumber && localInternationalNumber.length > 0;
      } else {
        if (companyInfo?.contactNumbers) {
          setTollFreeNumbers(companyInfo.contactNumbers.TOLL_FREE || []);
          setAllIndiaNumbers(companyInfo.contactNumbers.ALL_INDIA || []);
          setInternationalNumber(
            companyInfo.contactNumbers.INTERNATIONAL || [],
          );

          // Reset the expanded sections to their default state
          // We'll reuse the same logic as in getComapnyDetails
          hasTollFree =
            companyInfo.contactNumbers.TOLL_FREE &&
            companyInfo.contactNumbers.TOLL_FREE.length > 0;
          hasAllIndia =
            companyInfo.contactNumbers.ALL_INDIA &&
            companyInfo.contactNumbers.ALL_INDIA.length > 0;
          hasInternational =
            companyInfo.contactNumbers.INTERNATIONAL &&
            companyInfo.contactNumbers.INTERNATIONAL.length > 0;
        }
      }

      // Reset all sections to closed first
      setTollFreeCenter(false);
      setIndiaCallCenter(false);
      setInternationalCenter(false);

      // Then open the appropriate one
      if (hasTollFree) {
        setTollFreeCenter(true);
      } else if (hasAllIndia) {
        setIndiaCallCenter(true);
      } else if (hasInternational) {
        setInternationalCenter(true);
      }
      return;
    }

    let TOLL_FREE = companyInfo?.contactNumbers?.TOLL_FREE ?? [];
    let ALL_INDIA = companyInfo?.contactNumbers?.ALL_INDIA ?? [];
    let INTERNATIONAL = companyInfo?.contactNumbers?.INTERNATIONAL ?? [];

    // Filter contact numbers based on search query
    if (hasLocalDataLoad) {
      TOLL_FREE = localTollFreeNumbers ?? [];
      ALL_INDIA = localAllIndiaNumbers ?? [];
      INTERNATIONAL = localInternationalNumber ?? [];
    } else {
      TOLL_FREE = companyInfo?.contactNumbers?.TOLL_FREE ?? [];
      ALL_INDIA = companyInfo?.contactNumbers?.ALL_INDIA ?? [];
      INTERNATIONAL = companyInfo?.contactNumbers?.INTERNATIONAL ?? [];
    }
    const filteredNumbers = {
      tollFree:
        TOLL_FREE?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
      allIndia:
        ALL_INDIA?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
      international:
        INTERNATIONAL?.filter(
          item =>
            item.number.includes(query) ||
            item.description.toLowerCase().includes(query.toLowerCase()),
        ) || [],
    };

    setTollFreeNumbers(filteredNumbers.tollFree);
    setAllIndiaNumbers(filteredNumbers.allIndia);
    setInternationalNumber(filteredNumbers.international);

    // Determine which section to show based on search results
    const hasTollFreeResults = filteredNumbers.tollFree.length > 0;
    const hasAllIndiaResults = filteredNumbers.allIndia.length > 0;
    const hasInternationalResults = filteredNumbers.international.length > 0;

    // Reset all sections to closed first
    setTollFreeCenter(false);
    setIndiaCallCenter(false);
    setInternationalCenter(false);

    // Then open the appropriate one based on search results
    if (hasTollFreeResults) {
      setTollFreeCenter(true);
    } else if (hasAllIndiaResults) {
      setIndiaCallCenter(true);
    } else if (hasInternationalResults) {
      setInternationalCenter(true);
    }
  };

  const handleComplaintTapped = () => {};

  // Render a single company item
  const renderTollNumberItem = ({item}: {item: ContactNumber}) => (
    <CustomTollFreeNumbersCard
      numberData={item}
      onVoteUpdate={handleVoteUpdate}
      companyName={title}
    />
  );

  return (
    <SafeAreaView ref={screenContentRef} style={styles.container}>
      <ScrollView
        style={{
          backgroundColor: '#ffffff',
          flex: 1,
        }}
        contentContainerStyle={{
          backgroundColor: '#ffffff',
          paddingBottom: 20, // Add bottom padding for better screenshot
          minHeight: '100%', // Ensure full height coverage
          flexGrow: 1,
        }}
        showsVerticalScrollIndicator={false}>
        {/* Show Bank Holidays label only for Bank category companies and on specific holidays */}
        <BankHolidayBanner
          isVisible={isBankCategory}
          message={bankHolidayMessage}
        />
        {/* Temporarily hidden complaint information
        <View style={{paddingLeft: 15, paddingRight: 15}}>
          <Text
            style={[
              commonStyles.instructionText,
              commonStyles.instructionTextTopSpace,
            ]}>
            For Cyber Fraud Complaint: 1930
          </Text>
          <Text
            style={[
              commonStyles.instructionText,
              commonStyles.instructionTextBottomSpace,
            ]}>
            For Complaint Through RBI: 14440
          </Text>
        </View>
        */}
        <View style={{marginTop: 10}} />
        <CustomSearchBar
          onSearch={handleSearch}
          isSearching={isSearching}
          initialValue={searchQuery}
          placeholder="Search numbers"
        />
        {loading && (
          <View style={commonStyles.footerLoader}>
            <ActivityIndicator size="small" color="#0000ff" />
            <Text style={commonStyles.footerText}>Loading more...</Text>
          </View>
        )}
        {error && (
          <View style={commonStyles.errorContainer}>
            <Text style={commonStyles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => getComapnyDetails()}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
        {tollFreeNumbers.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setTollFreeCenter(!tollFreeCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>Toll Free Numbers</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    tollFreeCenter ? Images.ic_caretUp : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {tollFreeCenter && tollFreeNumbers.length > 0 && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={tollFreeNumbers}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        {allIndiaNumbers.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setIndiaCallCenter(!indiaCallCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>All India Number</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    indiaCallCenter ? Images.ic_caretUp : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {indiaCallCenter && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={allIndiaNumbers}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        {internationalNumber.length > 0 && (
          <View style={styles.itemCellView}>
            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => setInternationalCenter(!internationalCenter)}
              activeOpacity={0.7}>
              <Text style={styles.itemHeader}>International Number</Text>
              <View style={styles.cellHeaderIconContainer}>
                <Image
                  style={{
                    height: 25,
                    width: 25,
                  }}
                  source={
                    internationalCenter
                      ? Images.ic_caretUp
                      : Images.ic_caretDown
                  }
                />
              </View>
            </TouchableOpacity>
            {internationalCenter && (
              <FlatList
                style={styles.itemView}
                scrollEnabled={false}
                data={internationalNumber}
                keyExtractor={item => item.numberId.toString()}
                renderItem={renderTollNumberItem}
                showsVerticalScrollIndicator={false}
                showsHorizontalScrollIndicator={false}
              />
            )}
          </View>
        )}
        <Button
          mode="contained"
          onPress={handleComplaintTapped}
          style={styles.saveButton}
          labelStyle={styles.saveButtonLabel}
          buttonColor="#0a1d50">
          For Complaint
        </Button>
        <View>
          <View style={{flex: 1}}>
            <View style={styles.visitSiteView}>
              <Text style={styles.visitSiteKey}>Visit Official Site:</Text>
              <TouchableOpacity
                onPress={() => {
                  Linking.openURL(companyInfo?.companyWebsite ?? '');
                }}>
                <Text
                  numberOfLines={0}
                  ellipsizeMode="tail"
                  style={styles.siteLink}>
                  {companyInfo?.companyWebsite}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          {/* Temporarily hidden social media icons
          <View style={styles.imageContainer}>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_fb} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_insta} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_x} style={styles.image} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.imageTouchable}>
              <Image source={Images.ic_mail} style={styles.image} />
            </TouchableOpacity>
          </View>
          */}
        </View>
      </ScrollView>
      <NoteModalScreen
        visible={isModalVisible}
        title={title}
        content="Manage your notes for this company."
        companyId={companyId.toString()}
        onClose={() => setModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    marginBottom: 0,
  },
  safeAreaContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  itemCellView: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#D7E2F1',
    marginLeft: 15,
    marginRight: 15,
    marginTop: 15,
    padding: 3,
  },
  itemView: {
    paddingLeft: 15,
    paddingRight: 15,
    paddingBottom: 15,
    paddingTop: 6,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 5,
  },
  cellHeaderIconContainer: {
    height: 40,
    width: 40,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    margin: 15,
    marginTop: 30,
    paddingVertical: 6,
    borderRadius: 10,
  },
  saveButtonLabel: {
    fontSize: 16,
    color: COLORS.WHITE,
    fontFamily: FONTS.POPPINS.MEDIUM,
  },
  itemHeader: {
    marginLeft: 15,
    marginTop: 10,
    fontFamily: FONTS.POPPINS.MEDIUM,
    fontSize: 23,
  },
  imageContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 10,
  },
  imageTouchable: {
    height: 40,
    width: 40,
  },
  image: {
    height: 40,
    width: 40,
    resizeMode: 'contain',
  },
  headerIconsContainer: {
    flexDirection: 'row',
  },
  iconStyle: {
    width: 24,
    height: 24,
    marginHorizontal: 10,
  },
  visitSiteView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginHorizontal: 10,
    marginBottom: 10,
    padding: 10,
    borderRadius: 6,
    flexWrap: 'wrap',
  },
  visitSiteKey: {
    fontFamily: FONTS.POPPINS.SEMI_BOLD,
    fontSize: 17,
    color: '#000',
  },
  siteLink: {
    marginLeft: 5,
    fontFamily: FONTS.POPPINS.SEMI_BOLD,
    fontSize: 16,
    color: '#007FF5',
    textDecorationLine: 'underline',
    maxWidth: '100%',
  },
  retryButton: {
    backgroundColor: '#0a1d50',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default CompanyDetailsScreen;
