import { appSchema, tableSchema } from '@nozbe/watermelondb';

export default appSchema({
  version: 6,
  tables: [
    tableSchema({
      name: 'categories',
      columns: [
        { name: 'api_category_id', type: 'number' }, // Store original API categoryId
        { name: 'name', type: 'string' },
        { name: 'icon_url', type: 'string', isOptional: true },
        { name: 'is_active', type: 'number' },
        { name: 'category_priority', type: 'number' }, // Priority for sorting (lower values appear first)
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'companies',
      columns: [
        { name: 'company_id', type: 'number' }, // Store original API companyId
        { name: 'company_name', type: 'string' },
        { name: 'parent_company', type: 'string', isOptional: true },
        { name: 'company_email', type: 'string', isOptional: true },
        { name: 'company_logo_url', type: 'string', isOptional: true },
        { name: 'company_country', type: 'string', isOptional: true },
        { name: 'company_address', type: 'string', isOptional: true },
        { name: 'company_website', type: 'string', isOptional: true },
        { name: 'number', type: 'string', isOptional: true }, // Added number field
        { name: 'is_whatsapp', type: 'number' }, // 0 or 1, to track numbers
        { name: 'company_priority', type: 'number' }, // Priority for sorting (lower values appear first)
        { name: 'upvote_count', type: 'number' },
        { name: 'downvote_count', type: 'number' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'numbers',
      columns: [
        { name: 'api_number_id', type: 'number' }, // Store original API numberId
        { name: 'company_id', type: 'number' }, // API companyId
        { name: 'number', type: 'string' },
        { name: 'description', type: 'string', isOptional: true },
        { name: 'type', type: 'string' }, // TOLL_FREE, ALL_INDIA, INTERNATIONAL
        { name: 'upvote_count', type: 'number' },
        { name: 'downvote_count', type: 'number' },
        { name: 'is_whatsapp', type: 'number' }, // 0 or 1
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'history',
      columns: [
        { name: 'company_id', type: 'string' },
        { name: 'viewed_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'company_categories',
      columns: [
        { name: 'api_id', type: 'number' }, // Store original API id
        { name: 'company_id', type: 'number' }, // API companyId
        { name: 'category_id', type: 'number' }, // API categoryId
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'sync_state',
      columns: [
        { name: 'sync_key', type: 'string' },
        { name: 'status', type: 'string' }, // 'pending', 'in_progress', 'completed', 'failed'
        { name: 'progress', type: 'number' }, // 0-100 percentage
        { name: 'last_sync_at', type: 'number', isOptional: true },
        { name: 'last_data_time', type: 'number', isOptional: true }, // Epoch timestamp for lastDateTime API parameter
        { name: 'error_message', type: 'string', isOptional: true },
        { name: 'retry_count', type: 'number' },
        { name: 'created_at', type: 'number' },
        { name: 'updated_at', type: 'number' },
      ],
    }),
    tableSchema({
      name: 'notes',
      columns: [
        { name: 'note_id', type: 'string' }, // Primary key (unique identifier for note)
        { name: 'note_text', type: 'string' }, // The content/text of the note
        { name: 'company_id', type: 'string' }, // Foreign key (reference to company record)
        { name: 'created_at', type: 'number' }, // Timestamp when the note was created
        { name: 'deleted', type: 'boolean' }, // Soft-delete flag for sync safety (default: false)
      ],
    }),
  ],
});
