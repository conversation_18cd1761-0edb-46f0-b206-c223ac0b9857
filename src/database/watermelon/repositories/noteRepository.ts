import {Q} from '@nozbe/watermelondb';
import database from '../database';
import Note from '../models/Note';

export interface NoteData {
  id?: string; // WatermelonDB ID (string)
  noteId?: string; // Primary key (unique identifier for note)
  noteText: string; // The content/text of the note
  companyId: string; // Foreign key (reference to company record)
  createdAt?: Date; // Timestamp when the note was created
  deleted?: boolean; // Soft-delete flag for sync safety (default: false)
  reminderAt?: Date; // Timestamp for reminder notification
  notificationId?: string; // Push notification ID for cancellation
}

class WatermelonNoteRepository {
  private collection = database.get<Note>('notes');

  /**
   * Get all notes for a specific company (excluding soft-deleted notes)
   */
  async getNotesByCompanyId(companyId: string): Promise<NoteData[]> {
    try {
      const notes = await this.collection
        .query(
          Q.where('company_id', companyId),
          Q.where('deleted', false),
          Q.sortBy('created_at', Q.desc), // Most recent notes first
        )
        .fetch();

      return notes.map(note => ({
        id: note.id,
        noteId: note.noteId,
        noteText: note.noteText,
        companyId: note.companyId,
        createdAt: note.createdAt,
        deleted: note.deleted,
        reminderAt: note.reminderAt,
        notificationId: note.notificationId,
      }));
    } catch (error) {
      console.error('Error getting notes by company ID:', error);
      return [];
    }
  }

  /**
   * Create a new note for a company
   */
  async createNote(
    noteData: Omit<NoteData, 'id' | 'createdAt'>,
  ): Promise<string | null> {
    try {
      const newNote = await database.write(async () => {
        return await this.collection.create(note => {
          note.noteId = noteData.noteId || this.generateNoteId();
          note.noteText = noteData.noteText;
          note.companyId = noteData.companyId;
          note.deleted = noteData.deleted || false;
          note.reminderAt = noteData.reminderAt || undefined;
          note.notificationId = noteData.notificationId || undefined;
        });
      });

      console.log('Note created successfully:', newNote.id);
      return newNote.id;
    } catch (error) {
      console.error('Error creating note:', error);
      return null;
    }
  }

  /**
   * Soft delete a note (mark as deleted instead of removing from database)
   */
  async deleteNote(noteId: string): Promise<boolean> {
    try {
      await database.write(async () => {
        const note = await this.collection.find(noteId);
        await note.update(note => {
          note.deleted = true;
        });
      });

      console.log('Note soft deleted successfully:', noteId);
      return true;
    } catch (error) {
      console.error('Error deleting note:', error);
      return false;
    }
  }

  /**
   * Hard delete a note (permanently remove from database)
   * Use with caution - this is irreversible
   */
  async hardDeleteNote(noteId: string): Promise<boolean> {
    try {
      await database.write(async () => {
        const note = await this.collection.find(noteId);
        await note.destroyPermanently();
      });

      console.log('Note permanently deleted:', noteId);
      return true;
    } catch (error) {
      console.error('Error permanently deleting note:', error);
      return false;
    }
  }

  /**
   * Get a specific note by its ID
   */
  async getNoteById(noteId: string): Promise<NoteData | null> {
    try {
      const note = await this.collection.find(noteId);

      if (note && !note.deleted) {
        return {
          id: note.id,
          noteId: note.noteId,
          noteText: note.noteText,
          companyId: note.companyId,
          createdAt: note.createdAt,
          deleted: note.deleted,
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting note by ID:', error);
      return null;
    }
  }

  /**
   * Count total notes for a company (excluding soft-deleted)
   */
  async getNotesCountByCompanyId(companyId: string): Promise<number> {
    try {
      const count = await this.collection
        .query(Q.where('company_id', companyId), Q.where('deleted', false))
        .fetchCount();

      return count;
    } catch (error) {
      console.error('Error getting notes count:', error);
      return 0;
    }
  }

  /**
   * Generate a unique note ID
   */
  private generateNoteId(): string {
    return `note_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Update note with reminder information
   */
  async updateNoteReminder(
    noteId: string,
    reminderAt: Date | null,
    notificationId: string | null,
  ): Promise<boolean> {
    try {
      const note = await this.collection.find(noteId);

      await database.write(async () => {
        await note.update(note => {
          note.reminderAt = reminderAt || undefined;
          note.notificationId = notificationId || undefined;
        });
      });

      console.log(`Updated reminder for note ${noteId}:`, {
        reminderAt: reminderAt?.toISOString(),
        notificationId,
      });
      return true;
    } catch (error) {
      console.error('Error updating note reminder:', error);
      return false;
    }
  }

  /**
   * Get notes with active reminders
   */
  async getNotesWithReminders(): Promise<NoteData[]> {
    try {
      const notes = await this.collection
        .query(
          Q.where('deleted', false),
          Q.where('reminder_at', Q.notEq(null)),
          Q.sortBy('reminder_at', Q.asc),
        )
        .fetch();

      return notes.map(note => ({
        id: note.id,
        noteId: note.noteId,
        noteText: note.noteText,
        companyId: note.companyId,
        createdAt: note.createdAt,
        deleted: note.deleted,
        reminderAt: note.reminderAt,
        notificationId: note.notificationId,
      }));
    } catch (error) {
      console.error('Error getting notes with reminders:', error);
      return [];
    }
  }

  /**
   * Remove notes with expired reminders (past due date)
   */
  async removeExpiredReminderNotes(): Promise<number> {
    try {
      const now = new Date();
      const expiredNotes = await this.collection
        .query(
          Q.where('deleted', false),
          Q.where('reminder_at', Q.notEq(null)),
          Q.where('reminder_at', Q.lt(now.getTime())),
        )
        .fetch();

      if (expiredNotes.length > 0) {
        console.log(
          `Found ${expiredNotes.length} notes with expired reminders`,
        );

        await database.write(async () => {
          const batch = expiredNotes.map(note =>
            note.prepareUpdate(note => {
              note.deleted = true;
            }),
          );
          await database.batch(batch);
        });

        console.log(
          `Removed ${expiredNotes.length} notes with expired reminders`,
        );
        return expiredNotes.length;
      }

      return 0;
    } catch (error) {
      console.error('Error removing expired reminder notes:', error);
      return 0;
    }
  }

  /**
   * Clear all notes for a company (soft delete)
   */
  async clearNotesForCompany(companyId: string): Promise<boolean> {
    try {
      const notes = await this.collection
        .query(Q.where('company_id', companyId), Q.where('deleted', false))
        .fetch();

      await database.write(async () => {
        const batch = notes.map(note =>
          note.prepareUpdate(note => {
            note.deleted = true;
          }),
        );
        await database.batch(batch);
      });

      console.log(`Cleared ${notes.length} notes for company ${companyId}`);
      return true;
    } catch (error) {
      console.error('Error clearing notes for company:', error);
      return false;
    }
  }
}

const noteRepository = new WatermelonNoteRepository();
export default noteRepository;
