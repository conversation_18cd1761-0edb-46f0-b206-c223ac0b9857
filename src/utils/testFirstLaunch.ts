import appStateService from '../services/appStateService';

/**
 * Utility class for testing first launch functionality
 */
export class FirstLaunchTester {
  
  /**
   * Reset first launch flag to simulate fresh app installation
   * This will make the app show InfoScreen on next launch
   */
  static async simulateFreshInstall() {
    console.log('\n🔄 === SIMULATING FRESH APP INSTALL ===');
    
    try {
      await appStateService.resetFirstLaunchFlag();
      console.log('✅ First launch flag reset');
      console.log('📱 App will show InfoScreen on next launch');
      console.log('🔄 Please restart the app to test');
    } catch (error) {
      console.error('❌ Error simulating fresh install:', error);
    }
  }

  /**
   * Mark first launch as completed to simulate returning user
   * This will make the app show CategoryScreen on next launch
   */
  static async simulateReturningUser() {
    console.log('\n👤 === SIMULATING RETURNING USER ===');
    
    try {
      await appStateService.markFirstLaunchCompleted();
      console.log('✅ First launch marked as completed');
      console.log('📱 App will show CategoryScreen on next launch');
      console.log('🔄 Please restart the app to test');
    } catch (error) {
      console.error('❌ Error simulating returning user:', error);
    }
  }

  /**
   * Check current first launch status
   */
  static async checkCurrentStatus() {
    console.log('\n🔍 === CHECKING FIRST LAUNCH STATUS ===');
    
    try {
      const isFirst = await appStateService.isFirstLaunch();
      const version = await appStateService.getStoredAppVersion();
      
      console.log(`Is First Launch: ${isFirst}`);
      console.log(`Stored Version: ${version || 'Not set'}`);
      console.log(`Expected Initial Screen: ${isFirst ? 'InfoScreen' : 'CategoryScreen'}`);
    } catch (error) {
      console.error('❌ Error checking status:', error);
    }
  }

  /**
   * Clear all app state data
   */
  static async clearAllAppState() {
    console.log('\n🗑️ === CLEARING ALL APP STATE ===');
    
    try {
      await appStateService.clearAllAppState();
      console.log('✅ All app state cleared');
      console.log('📱 App will behave as fresh install on next launch');
    } catch (error) {
      console.error('❌ Error clearing app state:', error);
    }
  }

  /**
   * Run comprehensive test of first launch flow
   */
  static async runComprehensiveTest() {
    console.log('\n🧪 === COMPREHENSIVE FIRST LAUNCH TEST ===');
    
    try {
      // 1. Check initial status
      console.log('\n1️⃣ Initial Status:');
      await this.checkCurrentStatus();

      // 2. Simulate fresh install
      console.log('\n2️⃣ Simulating Fresh Install:');
      await this.simulateFreshInstall();
      await this.checkCurrentStatus();

      // 3. Simulate completing onboarding
      console.log('\n3️⃣ Simulating Onboarding Completion:');
      await this.simulateReturningUser();
      await this.checkCurrentStatus();

      console.log('\n✅ Comprehensive test completed');
      console.log('🔄 Restart the app to see the changes');
      
    } catch (error) {
      console.error('❌ Error running comprehensive test:', error);
    }
  }
}

// Export convenience functions for easy console access
export const testFirstLaunch = {
  simulateFreshInstall: FirstLaunchTester.simulateFreshInstall,
  simulateReturningUser: FirstLaunchTester.simulateReturningUser,
  checkStatus: FirstLaunchTester.checkCurrentStatus,
  clearAll: FirstLaunchTester.clearAllAppState,
  runTest: FirstLaunchTester.runComprehensiveTest,
};

// Make it available globally for console debugging
if (__DEV__) {
  (global as any).testFirstLaunch = testFirstLaunch;
  console.log('🔧 First launch testing utilities available as global.testFirstLaunch');
}
